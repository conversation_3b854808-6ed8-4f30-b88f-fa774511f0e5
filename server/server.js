const express = require('express');
const cors = require('cors');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');

const app = express();
const PORT = process.env.PORT || 3001;
const JWT_SECRET = 'your-secret-key-change-in-production';

// Middleware
app.use(cors());
app.use(express.json());

// Mock user database
const users = [
  {
    id: '1',
    name: 'Admin User',
    login: 'admin',
    email: '<EMAIL>',
    password: bcrypt.hashSync('admin123', 10), // password: admin123
    role: 'admin',
    contactNo: '+1234567890',
    notification: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: '2',
    name: '<PERSON>',
    login: 'john.doe',
    email: '<EMAIL>',
    password: bcrypt.hashSync('password123', 10), // password: password123
    role: 'user',
    contactNo: '+1234567891',
    notification: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];

// Mock customers database
const customers = [
  {
    id: '1',
    name: 'Acme Corporation',
    contactPerson: 'Jane Smith',
    contactNo: '+1234567890',
    email: '<EMAIL>',
    address1: '123 Business St',
    postcode: '12345',
    city: 'Business City',
    state: 'BC',
    country: 'USA',
    createBy: '1',
    isDelete: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];

// Middleware to verify JWT token
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ success: false, error: 'Access token required' });
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ success: false, error: 'Invalid or expired token' });
    }
    req.user = user;
    next();
  });
};

// Helper function to create user response (without password)
const createUserResponse = (user) => {
  const { password, ...userResponse } = user;
  return userResponse;
};

// Routes

// Health check
app.get('/api/v1/health', (req, res) => {
  res.json({ success: true, message: 'API server is running' });
});

// Auth routes
app.post('/api/v1/auth/login', async (req, res) => {
  try {
    const { login, password } = req.body;

    if (!login || !password) {
      return res.status(400).json({
        success: false,
        error: 'Username/email and password are required'
      });
    }

    // Find user by login or email
    const user = users.find(u => u.login === login || u.email === login);
    
    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'Invalid credentials'
      });
    }

    // Check password
    const isValidPassword = await bcrypt.compare(password, user.password);
    
    if (!isValidPassword) {
      return res.status(401).json({
        success: false,
        error: 'Invalid credentials'
      });
    }

    // Generate JWT token
    const token = jwt.sign(
      { 
        id: user.id, 
        login: user.login, 
        email: user.email,
        role: user.role 
      },
      JWT_SECRET,
      { expiresIn: '24h' }
    );

    res.json({
      success: true,
      data: {
        token,
        user: createUserResponse(user)
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

app.post('/api/v1/auth/logout', authenticateToken, (req, res) => {
  // In a real application, you might want to blacklist the token
  res.json({
    success: true,
    message: 'Logged out successfully'
  });
});

// User routes
app.get('/api/v1/users/me', authenticateToken, (req, res) => {
  const user = users.find(u => u.id === req.user.id);
  
  if (!user) {
    return res.status(404).json({
      success: false,
      error: 'User not found'
    });
  }

  res.json({
    success: true,
    data: createUserResponse(user)
  });
});

app.get('/api/v1/users', authenticateToken, (req, res) => {
  const userResponses = users.map(createUserResponse);
  res.json({
    success: true,
    data: userResponses
  });
});

app.get('/api/v1/users/:id', authenticateToken, (req, res) => {
  const user = users.find(u => u.id === req.params.id);
  
  if (!user) {
    return res.status(404).json({
      success: false,
      error: 'User not found'
    });
  }

  res.json({
    success: true,
    data: createUserResponse(user)
  });
});

// Customer routes
app.get('/api/v1/customers', authenticateToken, (req, res) => {
  res.json({
    success: true,
    data: customers
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    success: false,
    error: 'Something went wrong!'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Route not found'
  });
});

app.listen(PORT, () => {
  console.log(`API server running on http://localhost:${PORT}`);
  console.log('Available test accounts:');
  console.log('- Username: admin, Password: admin123');
  console.log('- Username: john.doe, Password: password123');
});
