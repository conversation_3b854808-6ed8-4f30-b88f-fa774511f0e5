# Authentication System Guide

## Overview
The JNX Project Manager uses a token-based authentication system that integrates directly with the API without using Pinia stores.

## Architecture

### Core Components
- **Auth Utility** (`src/utils/auth.ts`) - Manages authentication state and operations
- **API Service** (`src/services/api.ts`) - Handles API communication with automatic token headers
- **Router Guards** (`src/router/index.ts`) - Protects routes and validates tokens

## Authentication Flow

### 1. Login Process
```typescript
// User submits credentials
const result = await login({ login: '<EMAIL>', password: 'password' })

// Token extracted from response.data.token
const authToken = response.data.token || response.data.access_token || response.data.accessToken

// Token stored and set in API client
localStorage.setItem('auth_token', authToken)
AuthService.setToken(authToken)

// User profile fetched using token
const profileResponse = await AuthService.getCurrentUser()
```

### 2. Token Management
- **Storage**: localStorage with key `auth_token`
- **API Headers**: Automatically added as `Authorization: Bearer <token>`
- **Validation**: Profile API (`/auth/profile`) validates token on route changes

### 3. Route Protection
```typescript
// Navigation guard checks authentication
if (to.meta.requiresAuth) {
  if (!isAuthenticated.value) {
    // Redirect to login
  }
  
  // Validate token with profile API
  const isValid = await checkAuthStatus()
  if (!isValid) {
    // Clear invalid token and redirect
  }
}
```

## API Integration

### Endpoints Used
- **Login**: `POST /auth/login` - Returns `{ token, user }`
- **Profile**: `GET /auth/profile` - Validates token and returns user data
- **Logout**: `POST /auth/logout` - Invalidates token
- **Customers**: `GET /customers` - Requires authorization header

### Token Header Format
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## Usage Examples

### Check Authentication Status
```typescript
import { isAuthenticated, authState } from '@/utils/auth'

// Reactive authentication state
const isLoggedIn = isAuthenticated.value
const currentUser = authState.currentUser.value
const isLoading = authState.loading.value
```

### Login User
```typescript
import { login } from '@/utils/auth'

const handleLogin = async () => {
  const result = await login({
    login: '<EMAIL>',
    password: 'password'
  })
  
  if (result) {
    // Login successful, user redirected
  } else {
    // Handle login error
  }
}
```

### Logout User
```typescript
import { logout } from '@/utils/auth'

const handleLogout = async () => {
  await logout()
  // User redirected to login page
}
```

## Error Handling

### Common Scenarios
1. **Invalid Token**: Automatically cleared and user redirected to login
2. **Network Errors**: Graceful fallback with error messages
3. **API Errors**: Proper error propagation to UI components

### Debug Information
- Check browser console for authentication-related logs
- Inspect localStorage for `auth_token` value
- Monitor Network tab for API requests with Authorization headers

## Security Features

### Token Validation
- Automatic validation on app initialization
- Route-level validation before accessing protected pages
- Invalid tokens are automatically cleared

### Secure Storage
- Tokens stored in localStorage (consider httpOnly cookies for production)
- Automatic cleanup on logout or token expiration
- No sensitive data stored in client-side state

## Development Notes

### Current Status
- ✅ Core authentication working
- ✅ Profile API integration complete
- ✅ Token-based authorization implemented
- ✅ Route protection active
- ⚠️ Some modal components have TypeScript errors (non-critical)

### Future Improvements
- Implement refresh token mechanism
- Add token expiration handling
- Consider moving to httpOnly cookies
- Complete modal component refactoring
- Add comprehensive error boundaries
