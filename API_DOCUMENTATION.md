# API Documentation

This document describes the API implementation for the JNX Project Manager application.

## Base URL
```
https://cmsapi.jenexusenergy.com/api/v1/
```

## Authentication

The API uses Bearer token authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-token>
```

## API Services

### AuthService

#### Login
```typescript
AuthService.login({ login: 'username', password: 'password' })
```

#### Logout
```typescript
AuthService.logout()
```

#### Set Token
```typescript
AuthService.setToken('your-token')
```

### UserService

#### Get Current User
```typescript
UserService.getCurrentUser()
```

#### Get User by ID
```typescript
UserService.getUserById('user-id')
```

#### Get All Users
```typescript
UserService.getUsers()
```

#### Update User
```typescript
UserService.updateUser('user-id', { name: 'New Name' })
```

### CustomerService

#### Get All Customers
```typescript
CustomerService.getCustomers()
```

#### Get Customer by ID
```typescript
CustomerService.getCustomerById('customer-id')
```

#### Create Customer
```typescript
const customerData = {
  name: 'Company Name',
  contactPerson: 'John Doe',
  contactNo: '+1234567890',
  email: '<EMAIL>',
  address1: '123 Main St',
  postcode: '12345',
  city: 'City',
  state: 'State',
  country: 'Country'
}
CustomerService.createCustomer(customerData)
```

#### Update Customer
```typescript
CustomerService.updateCustomer('customer-id', { name: 'Updated Name' })
```

#### Delete Customer
```typescript
CustomerService.deleteCustomer('customer-id')
```

#### Search Customers
```typescript
CustomerService.searchCustomers('search-query')
```

#### Get Customers by Parent
```typescript
CustomerService.getCustomersByParent('parent-id')
```

## Data Models

### UserResponse
```typescript
interface UserResponse {
  id: string
  name: string
  login: string
  email: string
  role: string
  contactNo: string
  notification: boolean
  createdAt: string
  updatedAt: string
}
```

### Customer
```typescript
interface Customer {
  id: string
  parentId?: string
  name: string
  contactPerson: string
  contactPerson2?: string
  contactNo: string
  contactNo2?: string
  email: string
  email2?: string
  address1: string
  address2?: string
  postcode: string
  city: string
  state: string
  country: string
  createBy: string
  isDelete: boolean
  officeNo?: string
  officeNo2?: string
  fax?: string
  officeExtNo?: string
  officeExtNo2?: string
  fullName?: string
  createdAt: string
  updatedAt: string
}
```

## Stores (Pinia)

### useAuthStore
Handles authentication state and operations.

```typescript
const authStore = useAuthStore()

// Login
await authStore.login({ login: 'username', password: 'password' })

// Logout
await authStore.logout()

// Check if authenticated
console.log(authStore.isAuthenticated)
```

### useUsersStore
Manages user data and operations.

```typescript
const usersStore = useUsersStore()

// Fetch all users
await usersStore.fetchUsers()

// Get current user
await usersStore.fetchCurrentUser()

// Update user
await usersStore.updateUser('user-id', { name: 'New Name' })
```

### useCustomersStore
Manages customer data and operations.

```typescript
const customersStore = useCustomersStore()

// Fetch all customers
await customersStore.fetchCustomers()

// Add new customer
await customersStore.addCustomer(customerData)

// Update customer
await customersStore.updateCustomer('customer-id', updateData)

// Delete customer
await customersStore.deleteCustomer('customer-id')

// Search customers
const results = await customersStore.searchCustomers('query')
```

## Composables

### useApi
Generic composable for API operations.

```typescript
import { useApi } from '@/composables/useApi'
import { CustomerService } from '@/services/api'

const { data, loading, error, execute } = useApi(CustomerService.getCustomers)

// Execute the API call
await execute()

// Access the results
console.log(data.value) // Customer[]
console.log(loading.value) // boolean
console.log(error.value) // string | null
```

### useApiMutation
For form submissions and mutations.

```typescript
import { useApiMutation } from '@/composables/useApi'
import { CustomerService } from '@/services/api'

const { loading, error, success, mutate } = useApiMutation(CustomerService.createCustomer)

// Submit form
const result = await mutate(formData)
```

## Components

### CustomerForm.vue
A complete form component for creating/editing customers.

```vue
<template>
  <CustomerForm 
    :customer="selectedCustomer" 
    @success="handleSuccess" 
    @cancel="handleCancel" 
  />
</template>
```

### LoginForm.vue
Authentication form component.

```vue
<template>
  <LoginForm />
</template>
```

## Error Handling

All API responses follow this nested structure:

```typescript
// Outer response wrapper
interface ApiResponse<T> {
  success: boolean
  data?: InnerApiResponse<T>
  message?: string
  error?: string
}

// Inner response structure (what the server actually returns)
interface InnerApiResponse<T> {
  success: boolean
  data: T | null
  pagination?: PaginationInfo
  timestamp: string
}

interface PaginationInfo {
  page: number
  limit: number
  total: number
  pages: number
}
```

Errors are automatically handled by the stores and composables, but you can also handle them manually:

```typescript
const response = await CustomerService.getCustomers()
if (!response.success || !response.data?.success) {
  console.error('API Error:', response.error)
} else {
  const actualData = response.data.data
  const pagination = response.data.pagination
  const timestamp = response.data.timestamp
}
```

## Usage Examples

### Complete Customer Management Flow

```typescript
// In a Vue component
import { useCustomersStore } from '@/stores/customers'
import { onMounted } from 'vue'

const customersStore = useCustomersStore()

onMounted(async () => {
  // Load customers
  await customersStore.fetchCustomers()
})

// Create new customer
const createCustomer = async (formData) => {
  const result = await customersStore.addCustomer(formData)
  if (result) {
    console.log('Customer created:', result)
  } else {
    console.error('Error:', customersStore.error)
  }
}
```

### Authentication Flow

```typescript
// In a Vue component
import { useAuthStore } from '@/stores/auth'
import { useRouter } from 'vue-router'

const authStore = useAuthStore()
const router = useRouter()

const login = async (credentials) => {
  const result = await authStore.login(credentials)
  if (result) {
    router.push('/dashboard')
  } else {
    console.error('Login failed:', authStore.error)
  }
}
```

This API implementation provides a complete, type-safe interface for interacting with your backend API while maintaining clean separation of concerns and proper error handling.
