// API Response Types
export interface UserResponse {
  id: string
  name: string
  login: string
  email: string
  role: string
  contactNo: string
  notification: boolean
  createdAt: string
  updatedAt: string
}

export interface Customer {
  id: string
  parentId?: string
  name: string
  contactPerson: string
  contactPerson2?: string
  contactNo: string
  contactNo2?: string
  email: string
  email2?: string
  address1: string
  address2?: string
  postcode: string
  city: string
  state: string
  country: string
  createBy: string
  isDelete: boolean
  officeNo?: string
  officeNo2?: string
  fax?: string
  officeExtNo?: string
  officeExtNo2?: string
  fullName?: string
  createdAt: string
  updatedAt: string
}

// API Request Types
export interface CreateCustomerRequest {
  parentId?: string
  name: string
  contactPerson: string
  contactPerson2?: string
  contactNo: string
  contactNo2?: string
  email: string
  email2?: string
  address1: string
  address2?: string
  postcode: string
  city: string
  state: string
  country: string
  officeNo?: string
  officeNo2?: string
  fax?: string
  officeExtNo?: string
  officeExtNo2?: string
  fullName?: string
}

export interface UpdateCustomerRequest extends Partial<CreateCustomerRequest> {}

// API Response wrapper
export interface ApiResponse<T> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

export interface Equipment {
  id: string
  name: string
  model: string
  serialNumber: string
  customerId: string
  purchaseDate: Date
  expiryDate: Date
  status: 'active' | 'expired' | 'maintenance'
  description?: string
  createdAt: Date
  updatedAt: Date
}

export interface Event {
  id: string
  title: string
  description: string
  date: Date
  type: 'event' | 'equipment-expiry' | 'reminder'
  color: 'blue' | 'red' | 'green'
  equipmentId?: string
  customerId?: string
  createdAt: Date
  updatedAt: Date
}

export interface Ticket {
  id: string
  title: string
  description: string
  status: 'open' | 'in-progress' | 'resolved' | 'closed'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  customerId?: string
  equipmentId?: string
  assignedTo?: string
  createdBy: string
  createdAt: Date
  updatedAt: Date
  resolvedAt?: Date
}

export interface CalendarItem {
  id: string
  title: string
  date: Date
  type: 'event' | 'equipment-expiry' | 'reminder'
  color: 'blue' | 'red' | 'green'
  relatedId?: string
}
