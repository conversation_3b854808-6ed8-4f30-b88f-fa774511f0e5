@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom responsive utilities */
@layer utilities {
  /* Responsive text utilities - More subtle scaling */
  .text-responsive-xs {
    @apply text-xs tablet:text-sm desktop:text-sm;
  }

  .text-responsive-sm {
    @apply text-sm tablet:text-base desktop:text-base;
  }

  .text-responsive-base {
    @apply text-base tablet:text-lg desktop:text-lg;
  }

  .text-responsive-lg {
    @apply text-lg tablet:text-xl desktop:text-xl;
  }

  .text-responsive-xl {
    @apply text-xl tablet:text-2xl desktop:text-2xl;
  }

  .text-responsive-2xl {
    @apply text-2xl tablet:text-3xl desktop:text-3xl;
  }

  /* Responsive spacing utilities - More subtle scaling */
  .p-responsive {
    @apply p-4 tablet:p-5 desktop:p-6;
  }

  .px-responsive {
    @apply px-4 tablet:px-5 desktop:px-6;
  }

  .py-responsive {
    @apply py-4 tablet:py-5 desktop:py-6;
  }

  .m-responsive {
    @apply m-4 tablet:m-5 desktop:m-6;
  }

  .mx-responsive {
    @apply mx-4 tablet:mx-5 desktop:mx-6;
  }

  .my-responsive {
    @apply my-4 tablet:my-5 desktop:my-6;
  }

  .gap-responsive {
    @apply gap-4 tablet:gap-5 desktop:gap-6;
  }

  .space-x-responsive > * + * {
    @apply ml-4 tablet:ml-6 desktop:ml-8;
  }

  .space-y-responsive > * + * {
    @apply mt-4 tablet:mt-6 desktop:mt-8;
  }
}

/* Base responsive improvements */
@layer base {
  html {
    @apply scroll-smooth;
  }

  body {
    @apply antialiased;
  }

  /* Improve button touch targets on mobile */
  button, [role="button"] {
    @apply touch-manipulation;
  }

  /* Better focus styles */
  *:focus {
    @apply outline-none ring-2 ring-blue-500 ring-offset-2;
  }

  /* Responsive table improvements */
  .responsive-table {
    @apply min-w-full;
  }

  .responsive-table th,
  .responsive-table td {
    @apply px-4 tablet:px-6 desktop:px-8 py-3 tablet:py-4 desktop:py-5;
  }

  .responsive-table th {
    @apply text-xs tablet:text-sm desktop:text-base;
  }

  .responsive-table td {
    @apply text-sm tablet:text-base desktop:text-lg;
  }
}
