import { createRouter, createWebHistory } from 'vue-router'
import { isAuthenticated, checkAuthStatus } from '@/utils/auth'
import Dashboard from '@/views/Dashboard.vue'
import Customers from '@/views/Customers.vue'
import Equipment from '@/views/Equipment.vue'
import Tickets from '@/views/Tickets.vue'
import Login from '@/views/Login.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      name: 'login',
      component: Login,
      meta: {
        title: 'Login',
        requiresGuest: true
      }
    },
    {
      path: '/',
      name: 'dashboard',
      component: Dashboard,
      meta: {
        title: 'Dashboard',
        requiresAuth: true
      }
    },
    {
      path: '/customers',
      name: 'customers',
      component: Customers,
      meta: {
        title: 'Customers',
        requiresAuth: true
      }
    },
    {
      path: '/equipment',
      name: 'equipment',
      component: Equipment,
      meta: {
        title: 'Equipment',
        requiresAuth: true
      }
    },
    {
      path: '/tickets',
      name: 'tickets',
      component: Tickets,
      meta: {
        title: 'Tickets',
        requiresAuth: true
      }
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'not-found',
      redirect: '/'
    }
  ],
})

// Navigation guards
router.beforeEach(async (to, from, next) => {
  // Set page title
  if (to.meta.title) {
    document.title = `${to.meta.title} - JNX Manager`
  }

  // Check if route requires authentication
  if (to.meta.requiresAuth) {
    if (!isAuthenticated.value) {
      // Redirect to login with return URL
      next({
        name: 'login',
        query: { redirect: to.fullPath }
      })
      return
    }

    // Verify token is still valid using the profile API
    try {
      const isValid = await checkAuthStatus()
      if (!isValid) {
        next({
          name: 'login',
          query: { redirect: to.fullPath }
        })
        return
      }
    } catch (error) {
      console.error('Auth check failed:', error)
      next({
        name: 'login',
        query: { redirect: to.fullPath }
      })
      return
    }
  }

  // Check if route requires guest (not authenticated)
  if (to.meta.requiresGuest && isAuthenticated.value) {
    next({ name: 'dashboard' })
    return
  }

  next()
})

export default router
