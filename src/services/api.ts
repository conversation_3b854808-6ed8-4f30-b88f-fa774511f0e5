import type { 
  UserResponse, 
  Customer, 
  CreateCustomerRequest, 
  UpdateCustomerRequest, 
  ApiResponse 
} from '@/types'

// API Configuration
const API_BASE_URL = 'https://cmsapi.jenexusenergy.com/api/v1'

// API Client class
class ApiClient {
  private baseURL: string
  private defaultHeaders: Record<string, string>

  constructor(baseURL: string) {
    this.baseURL = baseURL
    this.defaultHeaders = {
      'Content-Type': 'application/json',
    }
  }

  // Set authorization token
  setAuthToken(token: string) {
    this.defaultHeaders['Authorization'] = `Bearer ${token}`
  }

  // Remove authorization token
  removeAuthToken() {
    delete this.defaultHeaders['Authorization']
  }

  // Generic request method
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const url = `${this.baseURL}${endpoint}`
      const config: RequestInit = {
        ...options,
        headers: {
          ...this.defaultHeaders,
          ...options.headers,
        },
      }

      const response = await fetch(url, config)
      const data = await response.json()

      if (!response.ok) {
        return {
          success: false,
          error: data.message || `HTTP error! status: ${response.status}`,
        }
      }

      return {
        success: true,
        data,
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      }
    }
  }

  // GET request
  async get<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'GET' })
  }

  // POST request
  async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  // PUT request
  async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  // DELETE request
  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE' })
  }
}

// Create API client instance
export const apiClient = new ApiClient(API_BASE_URL)

// User API Service
export class UserService {
  // Get current user profile
  static async getCurrentUser(): Promise<ApiResponse<UserResponse>> {
    return apiClient.get<UserResponse>('/auth/profile')
  }

  // Get user by ID
  static async getUserById(id: string): Promise<ApiResponse<UserResponse>> {
    return apiClient.get<UserResponse>(`/users/${id}`)
  }

  // Get all users
  static async getUsers(): Promise<ApiResponse<UserResponse[]>> {
    return apiClient.get<UserResponse[]>('/users')
  }

  // Update user profile
  static async updateUser(id: string, data: Partial<UserResponse>): Promise<ApiResponse<UserResponse>> {
    return apiClient.put<UserResponse>(`/users/${id}`, data)
  }
}

// Customer API Service
export class CustomerService {
  // Get all customers
  static async getCustomers(): Promise<ApiResponse<Customer[]>> {
    return apiClient.get<Customer[]>('/customers')
  }

  // Get customer by ID
  static async getCustomerById(id: string): Promise<ApiResponse<Customer>> {
    return apiClient.get<Customer>(`/customers/${id}`)
  }

  // Create new customer
  static async createCustomer(data: CreateCustomerRequest): Promise<ApiResponse<Customer>> {
    return apiClient.post<Customer>('/customers', data)
  }

  // Update customer
  static async updateCustomer(id: string, data: UpdateCustomerRequest): Promise<ApiResponse<Customer>> {
    return apiClient.put<Customer>(`/customers/${id}`, data)
  }

  // Delete customer (soft delete)
  static async deleteCustomer(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/customers/${id}`)
  }

  // Search customers
  static async searchCustomers(query: string): Promise<ApiResponse<Customer[]>> {
    return apiClient.get<Customer[]>(`/customers/search?q=${encodeURIComponent(query)}`)
  }

  // Get customers by parent ID
  static async getCustomersByParent(parentId: string): Promise<ApiResponse<Customer[]>> {
    return apiClient.get<Customer[]>(`/customers?parentId=${parentId}`)
  }
}

// Auth Service
export class AuthService {
  // Login
  static async login(credentials: { login: string; password: string }): Promise<ApiResponse<{ token: string; user: UserResponse }>> {
    return apiClient.post<{ token: string; user: UserResponse }>('/auth/login', credentials)
  }

  // Logout
  static async logout(): Promise<ApiResponse<void>> {
    const result = await apiClient.post<void>('/auth/logout')
    apiClient.removeAuthToken()
    return result
  }

  // Get current user profile
  static async getCurrentUser(): Promise<ApiResponse<UserResponse>> {
    return apiClient.get<UserResponse>('/auth/profile')
  }

  // Set token for authenticated requests
  static setToken(token: string) {
    apiClient.setAuthToken(token)
  }

  // Remove token
  static removeToken() {
    apiClient.removeAuthToken()
  }
}

// Export the API client for direct use if needed
export { apiClient as default }
