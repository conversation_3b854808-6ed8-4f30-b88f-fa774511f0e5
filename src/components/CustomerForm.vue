<template>
  <div class="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-md">
    <h2 class="text-2xl font-bold mb-6">{{ isEditing ? 'Edit Customer' : 'Add New Customer' }}</h2>
    
    <form @submit.prevent="handleSubmit" class="space-y-4">
      <!-- Basic Information -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label for="name" class="block text-sm font-medium text-gray-700">Company Name *</label>
          <input
            id="name"
            v-model="form.name"
            type="text"
            required
            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
          />
        </div>
        
        <div>
          <label for="fullName" class="block text-sm font-medium text-gray-700">Full Name</label>
          <input
            id="fullName"
            v-model="form.fullName"
            type="text"
            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
          />
        </div>
      </div>

      <!-- Contact Information -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label for="contactPerson" class="block text-sm font-medium text-gray-700">Contact Person *</label>
          <input
            id="contactPerson"
            v-model="form.contactPerson"
            type="text"
            required
            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
          />
        </div>
        
        <div>
          <label for="contactPerson2" class="block text-sm font-medium text-gray-700">Contact Person 2</label>
          <input
            id="contactPerson2"
            v-model="form.contactPerson2"
            type="text"
            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
          />
        </div>
      </div>

      <!-- Contact Numbers -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label for="contactNo" class="block text-sm font-medium text-gray-700">Contact Number *</label>
          <input
            id="contactNo"
            v-model="form.contactNo"
            type="tel"
            required
            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
          />
        </div>
        
        <div>
          <label for="contactNo2" class="block text-sm font-medium text-gray-700">Contact Number 2</label>
          <input
            id="contactNo2"
            v-model="form.contactNo2"
            type="tel"
            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
          />
        </div>
      </div>

      <!-- Email Addresses -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label for="email" class="block text-sm font-medium text-gray-700">Email *</label>
          <input
            id="email"
            v-model="form.email"
            type="email"
            required
            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
          />
        </div>
        
        <div>
          <label for="email2" class="block text-sm font-medium text-gray-700">Email 2</label>
          <input
            id="email2"
            v-model="form.email2"
            type="email"
            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
          />
        </div>
      </div>

      <!-- Address -->
      <div>
        <label for="address1" class="block text-sm font-medium text-gray-700">Address 1 *</label>
        <input
          id="address1"
          v-model="form.address1"
          type="text"
          required
          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
        />
      </div>

      <div>
        <label for="address2" class="block text-sm font-medium text-gray-700">Address 2</label>
        <input
          id="address2"
          v-model="form.address2"
          type="text"
          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
        />
      </div>

      <!-- Location Details -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label for="postcode" class="block text-sm font-medium text-gray-700">Postcode *</label>
          <input
            id="postcode"
            v-model="form.postcode"
            type="text"
            required
            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
          />
        </div>
        
        <div>
          <label for="city" class="block text-sm font-medium text-gray-700">City *</label>
          <input
            id="city"
            v-model="form.city"
            type="text"
            required
            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
          />
        </div>
        
        <div>
          <label for="state" class="block text-sm font-medium text-gray-700">State *</label>
          <input
            id="state"
            v-model="form.state"
            type="text"
            required
            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
          />
        </div>
      </div>

      <div>
        <label for="country" class="block text-sm font-medium text-gray-700">Country *</label>
        <input
          id="country"
          v-model="form.country"
          type="text"
          required
          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
        />
      </div>

      <!-- Error Message -->
      <div v-if="error" class="text-red-600 text-sm">
        {{ error }}
      </div>

      <!-- Submit Button -->
      <div class="flex justify-end space-x-3">
        <button
          type="button"
          @click="$emit('cancel')"
          class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          Cancel
        </button>
        <button
          type="submit"
          :disabled="loading"
          class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
        >
          {{ loading ? 'Saving...' : (isEditing ? 'Update Customer' : 'Create Customer') }}
        </button>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import type { Customer, CreateCustomerRequest, UpdateCustomerRequest } from '@/types'
import { CustomerService } from '@/services/api'

interface Props {
  customer?: Customer
}

const props = defineProps<Props>()
const emit = defineEmits<{
  success: [customer: Customer]
  cancel: []
}>()

const loading = ref(false)
const error = ref<string | null>(null)

const isEditing = computed(() => !!props.customer)

const form = reactive<CreateCustomerRequest>({
  name: '',
  contactPerson: '',
  contactPerson2: '',
  contactNo: '',
  contactNo2: '',
  email: '',
  email2: '',
  address1: '',
  address2: '',
  postcode: '',
  city: '',
  state: '',
  country: '',
  officeNo: '',
  officeNo2: '',
  fax: '',
  officeExtNo: '',
  officeExtNo2: '',
  fullName: ''
})

onMounted(() => {
  if (props.customer) {
    Object.assign(form, {
      name: props.customer.name,
      contactPerson: props.customer.contactPerson,
      contactPerson2: props.customer.contactPerson2,
      contactNo: props.customer.contactNo,
      contactNo2: props.customer.contactNo2,
      email: props.customer.email,
      email2: props.customer.email2,
      address1: props.customer.address1,
      address2: props.customer.address2,
      postcode: props.customer.postcode,
      city: props.customer.city,
      state: props.customer.state,
      country: props.customer.country,
      officeNo: props.customer.officeNo,
      officeNo2: props.customer.officeNo2,
      fax: props.customer.fax,
      officeExtNo: props.customer.officeExtNo,
      officeExtNo2: props.customer.officeExtNo2,
      fullName: props.customer.fullName
    })
  }
})

const handleSubmit = async () => {
  loading.value = true
  error.value = null

  try {
    let response

    if (isEditing.value && props.customer) {
      response = await CustomerService.updateCustomer(props.customer.id, form as UpdateCustomerRequest)
    } else {
      response = await CustomerService.createCustomer(form)
    }

    if (response.success && response.data && response.data.success && response.data.data) {
      emit('success', response.data.data)
    } else {
      error.value = response.error || 'Failed to save customer'
    }
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Unknown error occurred'
  } finally {
    loading.value = false
  }
}
</script>
