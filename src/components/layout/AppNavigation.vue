<template>
  <!-- Mobile menu overlay -->
  <div
    v-if="mobileMenuOpen"
    class="fixed inset-0 bg-gray-600 bg-opacity-75 lg:hidden z-40"
    @click="closeMobileMenu"
  ></div>

  <!-- Sidebar -->
  <div
    class="fixed inset-y-0 left-0 z-[60] w-64 tablet:w-72 desktop:w-72 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0"
    :class="{
      'translate-x-0': mobileMenuOpen,
      '-translate-x-full': !mobileMenuOpen
    }"
  >
    <div class="flex h-full flex-col">
      <!-- Logo -->
      <div class="flex h-16 tablet:h-18 desktop:h-20 shrink-0 items-center px-6 sm:px-8 tablet:px-8 border-b border-gray-200">
        <h1 class="text-lg sm:text-xl tablet:text-xl font-bold text-gray-900 truncate">JNX Project Manager</h1>
      </div>

      <!-- Navigation -->
      <nav class="flex-1 px-6 tablet:px-8 desktop:px-6 py-6 tablet:py-6 space-y-1 tablet:space-y-1 overflow-y-auto">
        <router-link
          v-for="item in navigation"
          :key="item.name"
          :to="item.href"
          @click="closeMobileMenu"
          class="group flex items-center px-4 tablet:px-5 desktop:px-4 py-3 tablet:py-3 text-sm tablet:text-base desktop:text-base font-medium rounded-md transition-colors touch-manipulation ring-0"
          :class="[
            $route.path === item.href
              ? 'bg-blue-50 text-blue-700 border-r-2 tablet:border-r-3 border-blue-700'
              : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900 active:bg-gray-100'
          ]"
        >
          <svg v-if="item.name === 'Dashboard'" class="mr-3 tablet:mr-4 desktop:mr-4 h-5 w-5 tablet:h-5 tablet:w-5 desktop:h-6 desktop:w-6 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
          </svg>
          <svg v-else-if="item.name === 'Customers'" xmlns="http://www.w3.org/2000/svg" class="mr-3 tablet:mr-4 desktop:mr-4 h-5 w-5 tablet:h-5 tablet:w-5 desktop:h-6 desktop:w-6 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z" />
          </svg>
          <svg v-else-if="item.name === 'Equipment'" class="mr-3 tablet:mr-4 desktop:mr-4 h-5 w-5 tablet:h-5 tablet:w-5 desktop:h-6 desktop:w-6 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
          <svg v-else-if="item.name === 'Tickets'" class="mr-3 tablet:mr-4 desktop:mr-4 h-5 w-5 tablet:h-5 tablet:w-5 desktop:h-6 desktop:w-6 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z" />
          </svg>
          {{ item.name }}
        </router-link>
      </nav>

      <!-- User section -->
      <div class="border-t border-gray-200 px-6 tablet:px-8 desktop:px-6 py-4 tablet:py-5 desktop:py-6 flex-shrink-0">
        <div class="flex items-center justify-between">
          <div class="flex items-center min-w-0 flex-1">
            <div class="h-8 w-8 tablet:h-9 tablet:w-9 desktop:h-10 desktop:w-10 rounded-full bg-blue-500 flex items-center justify-center flex-shrink-0">
              <span class="text-sm tablet:text-sm desktop:text-base font-medium text-white">
                {{ currentUser?.name?.charAt(0).toUpperCase() || 'U' }}
              </span>
            </div>
            <div class="ml-3 tablet:ml-4 desktop:ml-4 min-w-0 flex-1">
              <p class="text-sm tablet:text-base desktop:text-base font-medium text-gray-900 truncate">{{ currentUser?.name || currentUser?.login || 'User' }}</p>
              <p class="text-xs tablet:text-sm desktop:text-sm text-gray-500 truncate">{{ currentUser?.email || '<EMAIL>' }}</p>
            </div>
          </div>
          <button
            @click="handleLogout"
            class="p-1 tablet:p-2 desktop:p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 flex-shrink-0 ml-2"
            title="Logout"
          >
            <svg class="h-5 w-5 tablet:h-5 tablet:w-5 desktop:h-6 desktop:w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Mobile header -->
  <div class="lg:hidden fixed top-0 left-0 right-0 z-30 bg-white shadow-sm border-b border-gray-200">
    <div class="flex items-center justify-between px-4 py-3">
      <h1 class="text-lg font-semibold text-gray-900 truncate pr-2">JNX Manager</h1>
      <button
        @click="toggleMobileMenu"
        class="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 touch-manipulation flex-shrink-0"
        :aria-expanded="mobileMenuOpen"
        aria-label="Toggle navigation menu"
      >
        <svg
          class="h-6 w-6 transition-transform duration-200"
          :class="{ 'rotate-90': mobileMenuOpen }"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            v-if="!mobileMenuOpen"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M4 6h16M4 12h16M4 18h16"
          />
          <path
            v-else
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M6 18L18 6M6 6l12 12"
          />
        </svg>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { logout, authState } from '@/utils/auth'

const router = useRouter()
const route = useRoute()
const mobileMenuOpen = ref(false)

// Get current user from auth state
const currentUser = computed(() => authState.currentUser.value)

// Navigation items

// Navigation items
const navigation = [
  {
    name: 'Dashboard',
    href: '/'
  },
  {
    name: 'Customers',
    href: '/customers'
  },
  {
    name: 'Equipment',
    href: '/equipment'
  },
  {
    name: 'Tickets',
    href: '/tickets'
  }
]

const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value
}

const closeMobileMenu = () => {
  mobileMenuOpen.value = false
}

const handleLogout = async () => {
  try {
    await logout()
    router.push('/login')
  } catch (error) {
    console.error('Logout failed:', error)
    // Force logout even if API call fails
    router.push('/login')
  }
}

// Close mobile menu when route changes
watch(route, () => {
  closeMobileMenu()
})

// Handle keyboard events
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && mobileMenuOpen.value) {
    closeMobileMenu()
  }
}

// Add keyboard event listener
onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

// Remove keyboard event listener
onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})

</script>
