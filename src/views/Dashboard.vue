<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <h1 class="text-2xl font-bold text-gray-900">Dashboard</h1>
      <button
        @click="showAddEventModal = true"
        class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors font-medium"
      >
        Add Event
      </button>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 tablet:grid-cols-2 desktop:grid-cols-4 gap-4 tablet:gap-5 desktop:gap-6">
      <div class="bg-white p-6 tablet:p-6 desktop:p-6 rounded-lg shadow hover:shadow-md transition-shadow">
        <div class="flex items-center">
          <div class="p-2 tablet:p-3 desktop:p-3 bg-blue-100 rounded-lg">
            <svg class="w-6 h-6 tablet:w-7 tablet:h-7 desktop:w-8 desktop:h-8 text-blue-600" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z" />
            </svg>
          </div>
          <div class="ml-4 tablet:ml-5 desktop:ml-6">
            <p class="text-sm tablet:text-base desktop:text-base font-medium text-gray-600">Total Customers</p>
            <p class="text-2xl tablet:text-3xl desktop:text-3xl font-semibold text-gray-900">{{ customers.length }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white p-6 tablet:p-6 desktop:p-6 rounded-lg shadow hover:shadow-md transition-shadow">
        <div class="flex items-center">
          <div class="p-2 tablet:p-3 desktop:p-3 bg-green-100 rounded-lg">
            <svg class="w-6 h-6 tablet:w-7 tablet:h-7 desktop:w-8 desktop:h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div class="ml-4 tablet:ml-5 desktop:ml-6">
            <p class="text-sm tablet:text-base desktop:text-base font-medium text-gray-600">Active Equipment</p>
            <p class="text-2xl tablet:text-3xl desktop:text-3xl font-semibold text-gray-900">{{ equipment.length }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white p-6 tablet:p-6 desktop:p-6 rounded-lg shadow hover:shadow-md transition-shadow">
        <div class="flex items-center">
          <div class="p-2 tablet:p-3 desktop:p-3 bg-yellow-100 rounded-lg">
            <svg class="w-6 h-6 tablet:w-7 tablet:h-7 desktop:w-8 desktop:h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <div class="ml-4 tablet:ml-5 desktop:ml-6">
            <p class="text-sm tablet:text-base desktop:text-base font-medium text-gray-600">Expiring Soon</p>
            <p class="text-2xl tablet:text-3xl desktop:text-3xl font-semibold text-gray-900">{{ expiringEquipment.length }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white p-6 tablet:p-6 desktop:p-6 rounded-lg shadow hover:shadow-md transition-shadow">
        <div class="flex items-center">
          <div class="p-2 tablet:p-3 desktop:p-3 bg-red-100 rounded-lg">
            <svg class="w-6 h-6 tablet:w-7 tablet:h-7 desktop:w-8 desktop:h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z" />
            </svg>
          </div>
          <div class="ml-4 tablet:ml-5 desktop:ml-6">
            <p class="text-sm tablet:text-base desktop:text-base font-medium text-gray-600">Open Tickets</p>
            <p class="text-2xl tablet:text-3xl desktop:text-3xl font-semibold text-gray-900">{{ openTickets.length }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Calendar -->
    <div class="bg-white rounded-lg shadow">
      <CalendarComponent />
    </div>

    <!-- Recent Activity -->
    <div class="grid grid-cols-1 tablet:grid-cols-1 desktop:grid-cols-2 gap-6 tablet:gap-8 desktop:gap-10">
      <!-- Upcoming Events -->
      <div class="bg-white rounded-lg shadow hover:shadow-md transition-shadow">
        <div class="p-6 tablet:p-8 desktop:p-10 border-b border-gray-200">
          <h3 class="text-lg tablet:text-xl desktop:text-2xl font-medium text-gray-900">Upcoming Events</h3>
        </div>
        <div class="p-6 tablet:p-8 desktop:p-10">
          <div v-if="upcomingEvents.length === 0" class="text-gray-500 text-center py-4 tablet:py-6 desktop:py-8 text-sm tablet:text-base desktop:text-lg">
            No upcoming events
          </div>
          <div v-else class="space-y-3 tablet:space-y-4 desktop:space-y-5">
            <div
              v-for="event in upcomingEvents"
              :key="event.id"
              class="flex items-center space-x-3 tablet:space-x-4 desktop:space-x-5"
            >
              <div
                class="w-3 h-3 tablet:w-4 tablet:h-4 desktop:w-5 desktop:h-5 rounded-full"
                :class="{
                  'bg-blue-500': event.color === 'blue',
                  'bg-red-500': event.color === 'red',
                  'bg-green-500': event.color === 'green'
                }"
              ></div>
              <div class="flex-1">
                <p class="text-sm tablet:text-base desktop:text-lg font-medium text-gray-900">{{ event.title }}</p>
                <p class="text-xs tablet:text-sm desktop:text-base text-gray-500">{{ formatDate(event.date) }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Recent Tickets -->
      <div class="bg-white rounded-lg shadow">
        <div class="p-6 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Recent Tickets</h3>
        </div>
        <div class="p-6">
          <div v-if="recentTickets.length === 0" class="text-gray-500 text-center py-4">
            No recent tickets
          </div>
          <div v-else class="space-y-3">
            <div
              v-for="ticket in recentTickets"
              :key="ticket.id"
              class="flex items-center justify-between"
            >
              <div class="flex-1">
                <p class="text-sm font-medium text-gray-900">{{ ticket.title }}</p>
                <p class="text-xs text-gray-500">{{ formatDate(ticket.createdAt) }}</p>
              </div>
              <span
                class="px-2 py-1 text-xs font-medium rounded-full"
                :class="{
                  'bg-green-100 text-green-800': ticket.status === 'resolved',
                  'bg-blue-100 text-blue-800': ticket.status === 'in-progress',
                  'bg-yellow-100 text-yellow-800': ticket.status === 'open',
                  'bg-gray-100 text-gray-800': ticket.status === 'closed'
                }"
              >
                {{ ticket.status }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Add Event Modal -->
    <AddEventModal v-if="showAddEventModal" @close="showAddEventModal = false" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { CustomerService } from '@/services/api'
import type { Customer, Equipment, Event, Ticket } from '@/types'
import CalendarComponent from '@/components/calendar/CalendarComponent.vue'
import AddEventModal from '@/components/modals/AddEventModal.vue'

// Reactive data
const customers = ref<Customer[]>([])
const equipment = ref<Equipment[]>([])
const events = ref<Event[]>([])
const tickets = ref<Ticket[]>([])
const loading = ref(false)
const error = ref<string | null>(null)

const showAddEventModal = ref(false)

// Computed properties for filtered data
const expiringEquipment = computed(() => {
  const now = new Date()
  const thirtyDaysFromNow = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000)
  return equipment.value.filter(item => {
    const expiryDate = new Date(item.expiryDate)
    return expiryDate >= now && expiryDate <= thirtyDaysFromNow
  })
})

const openTickets = computed(() => {
  return tickets.value.filter(ticket => ticket.status === 'open' || ticket.status === 'in-progress')
})

const upcomingEvents = computed(() => {
  const now = new Date()
  return events.value
    .filter(event => new Date(event.date) >= now)
    .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
    .slice(0, 5)
})

const recentTickets = computed(() => {
  return tickets.value
    .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
    .slice(0, 5)
})

const formatDate = (date: Date) => {
  return new Date(date).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  })
}

onMounted(async () => {
  loading.value = true
  error.value = null

  try {
    // Load customers from API
    const customersResponse = await CustomerService.getCustomers()
    if (customersResponse.success && customersResponse.data && customersResponse.data.success && customersResponse.data.data) {
      customers.value = customersResponse.data.data
    } else if (customersResponse.success && customersResponse.data && customersResponse.data.success && customersResponse.data.data === null) {
      // Handle case where API returns null data (empty list)
      customers.value = []
    }

    // For now, initialize with sample data for equipment, events, and tickets
    // since we don't have those API endpoints implemented yet
    equipment.value = [
      {
        id: '1',
        name: 'Solar Panel System A',
        model: 'SP-2000',
        serialNumber: 'SP001',
        customerId: customers.value[0]?.id || '1',
        purchaseDate: new Date('2023-01-15'),
        expiryDate: new Date('2025-01-15'),
        status: 'active',
        description: 'Main solar panel system',
        createdAt: new Date('2023-01-15'),
        updatedAt: new Date('2023-01-15')
      }
    ]

    events.value = [
      {
        id: '1',
        title: 'Equipment Maintenance',
        description: 'Scheduled maintenance for solar panels',
        date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
        type: 'event',
        color: 'blue',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ]

    tickets.value = [
      {
        id: '1',
        title: 'System Performance Issue',
        description: 'Solar panel system showing reduced efficiency',
        status: 'open',
        priority: 'medium',
        customerId: customers.value[0]?.id || '1',
        createdBy: 'user1',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ]
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to load dashboard data'
    console.error('Dashboard data loading error:', err)
  } finally {
    loading.value = false
  }
})
</script>
